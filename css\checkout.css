/* Checkout Page Styles */

/* Variables */
:root {
  --primary-color: #ea9c00;
  --secondary-color: #282f39;
  --success-color: #12b76a;
  --warning-color: #f79009;
  --danger-color: #f04438;
  --light-color: #98a2b3;
  --dark-color: #1d2939;
  --gray-color: #98a2b3;
  --body-bg: #f4f7fd;
  --border-color: rgba(152, 162, 179, 0.25);
}

/* Checkout Container */
.checkout-container {
  padding: 30px 0;
  background-color: var(--body-bg);
}

/* Checkout Header */
.checkout-header {
  margin-bottom: 30px;
  text-align: center;
}

.checkout-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 10px;
}

/* Checkout Progress */
.checkout-progress {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  position: relative;
}

.checkout-progress::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--border-color);
  transform: translateY(-50%);
  z-index: 1;
}

.progress-step {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.333%;
}

.step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
  z-index: 2;
}

.step-icon i {
  color: var(--light-color);
  font-size: 20px;
}

.step-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--light-color);
}

.progress-step.active .step-icon {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}

.progress-step.active .step-icon i {
  color: #fff;
}

.progress-step.active .step-label {
  color: var(--dark-color);
  font-weight: 600;
}

.progress-step.completed .step-icon {
  border-color: var(--success-color);
  background-color: var(--success-color);
}

.progress-step.completed .step-icon i {
  color: #fff;
}

/* Checkout Forms */
.checkout-form {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.checkout-form h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--dark-color);
  margin-bottom: 8px;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* Address Cards */
.address-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.address-card {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 20px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
}

.address-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.address-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(234, 156, 0, 0.05);
}

.address-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.address-card-title {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-badge {
  background-color: var(--primary-color);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.address-card-actions {
  display: flex;
  gap: 10px;
}

.address-card-actions a {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--light-color);
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-card-actions a:hover {
  color: var(--primary-color);
}

.address-card-body {
  font-size: 14px;
  color: var(--secondary-color);
  line-height: 1.6;
}

.address-card-body p {
  margin: 0 0 5px;
}

/* Add New Address Card */
.add-new-address {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--border-color);
  background-color: #f9fafb;
}

.add-new-address:hover {
  border-color: var(--primary-color);
  background-color: #f9fafb;
}

.add-address-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: var(--dark-color);
}

.add-address-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  color: var(--dark-color);
}

.add-address-text {
  font-weight: 500;
  font-size: 14px;
}

/* Or Divider */
.or-divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
  cursor: pointer;
}

.or-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--border-color);
  z-index: 1;
}

.or-divider span {
  position: relative;
  z-index: 2;
  background-color: #fff;
  padding: 0 15px;
  color: var(--light-color);
  font-size: 14px;
}

/* Saved Addresses Section */
.saved-addresses-section,
.saved-shipping-addresses-section {
  margin-bottom: 30px;
}

.saved-addresses-section h4,
.saved-shipping-addresses-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
}

.address-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* Address Card Styles for Checkout */
.address-card {
  background: #fff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 200px;
}

.address-card:hover,
.address-card.hover {
  border-color: #ea9c00;
  box-shadow: 0 4px 12px rgba(234, 156, 0, 0.15);
  transform: translateY(-2px);
}

.address-card.selected {
  border-color: #ea9c00;
  background: linear-gradient(135deg, rgba(234, 156, 0, 0.05) 0%, rgba(245, 158, 11, 0.05) 100%);
  box-shadow: 0 4px 12px rgba(234, 156, 0, 0.2);
}

.address-card.selected::before {
  content: '';
  position: absolute;
  top: 15px;
  right: 15px;
  width: 24px;
  height: 24px;
  background: #ea9c00;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-card.selected::after {
  content: '✓';
  position: absolute;
  top: 19px;
  right: 19px;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.address-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.address-card-header .address-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.address-card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.address-card-type {
  margin-bottom: 16px;
}

.address-type-badge {
  display: inline-block;
  background: #ea9c00;
  color: white;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 20px;
}

/* Shipping address badge styling */
.shipping-address-card .address-type-badge {
  background: #10b981;
}

.address-card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.address-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.address-item-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  margin-top: 2px;
  flex-shrink: 0;
}

.address-item-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

/* Add New Address Card */
.address-card.add-new-address {
  border: 2px dashed #d1d5db;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.address-card.add-new-address:hover {
  border-color: #ea9c00;
  background: rgba(234, 156, 0, 0.05);
}

.address-empty {
  text-align: center;
}

.address-empty-icon {
  margin-bottom: 12px;
}

.address-empty-icon i {
  width: 32px;
  height: 32px;
  color: #9ca3af;
}

.address-empty-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* Hidden Form */
.hidden-form {
  display: none;
}

.saved-addresses-section.active-selection {
  margin-bottom: 0;
}

/* Responsive Design for Address Cards */
@media (max-width: 768px) {
  .address-cards-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .address-card {
    padding: 16px;
    min-height: auto;
  }

  .address-card-header h4 {
    font-size: 14px;
  }

  .address-item-text {
    font-size: 13px;
  }

  .saved-addresses-section h4 {
    font-size: 16px;
    margin-bottom: 15px;
  }
}

@media (max-width: 480px) {
  .address-card {
    padding: 12px;
  }

  .address-card-header {
    gap: 8px;
    margin-bottom: 12px;
  }

  .address-card-content {
    gap: 8px;
  }

  .address-item {
    gap: 8px;
  }
}





/* Payment Methods */
.payment-methods {
  margin-bottom: 30px;
}

.payment-method {
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method:hover {
  border-color: var(--primary-color);
}

.payment-method.selected {
  border-color: var(--primary-color);
  background-color: rgba(234, 156, 0, 0.05);
}

.payment-method-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.payment-method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f4f7fd;
  border-radius: 8px;
}

.payment-method-icon i {
  font-size: 20px;
  color: var(--primary-color);
}

.payment-method-title {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 16px;
}

/* Order Summary */
.order-summary {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 20px;
}

.order-summary h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 16px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-primary:hover {
  background-color: #d48c00;
}

.btn-secondary {
  background-color: #fff;
  color: var(--dark-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--dark-color);
}

.btn-block {
  display: block;
  width: 100%;
}

.checkout-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 40px 0;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: var(--success-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
}

.success-icon i {
  font-size: 40px;
  color: #fff;
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.success-message p {
  font-size: 16px;
  color: var(--light-color);
  margin-bottom: 30px;
}

/* Enhanced Responsive Styles */

/* Tablet Responsive (768px - 991px) */
@media (max-width: 991px) and (min-width: 769px) {
  .checkout-container {
    padding: 20px 0;
  }

  .checkout-form {
    padding: 25px;
  }

  .checkout-progress {
    margin-bottom: 30px;
  }

  .progress-step {
    margin: 0 10px;
  }

  .step-icon {
    width: 45px;
    height: 45px;
  }

  .step-label {
    font-size: 13px;
  }
}

/* Mobile Responsive (768px and below) */
@media (max-width: 768px) {

  /* Checkout container mobile optimization */
  .checkout-container {
    padding: 15px 0;
    background-color: #f8f9fa;
  }

  .checkout-container .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* Checkout header mobile */
  .checkout-header {
    margin-bottom: 20px;
    text-align: center;
    padding: 0 10px;
  }

  .checkout-header h2 {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .checkout-header p {
    font-size: 14px;
    color: var(--light-color);
    line-height: 1.4;
  }

  /* Progress steps mobile optimization */
  .checkout-progress {
    flex-direction: column;
    align-items: center;
    margin-bottom: 25px;
    gap: 15px;
  }

  .progress-step {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 280px;
    padding: 12px 16px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 0;
    transition: all 0.3s ease;
  }

  .progress-step.active {
    background: rgba(234, 156, 0, 0.1);
    border: 1px solid var(--primary-color);
  }

  .progress-step.completed {
    background: rgba(18, 183, 106, 0.1);
    border: 1px solid var(--success-color);
  }

  .step-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .step-label {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
  }

  /* Checkout form mobile optimization */
  .checkout-form {
    padding: 20px 15px;
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .checkout-form h3 {
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 10px;
  }

  /* Form elements mobile optimization */
  .form-group {
    margin-bottom: 18px;
  }

  .form-label {
    font-size: 14px;
    margin-bottom: 6px;
    font-weight: 500;
  }

  .form-control {
    padding: 14px 16px;
    font-size: 16px;
    /* Prevents zoom on iOS */
    border-radius: 10px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
  }

  .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
    outline: none;
  }

  /* Select dropdowns mobile */
  select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    appearance: none;
  }

  /* Checkbox and radio mobile styling */
  input[type="checkbox"],
  input[type="radio"] {
    transform: scale(1.2);
    margin-right: 8px;
  }

  .form-check-label {
    font-size: 14px;
    line-height: 1.4;
    cursor: pointer;
  }

  /* Address cards mobile */
  .address-cards {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 20px;
  }

  .address-card {
    padding: 16px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
  }

  .address-card.selected {
    border-color: var(--primary-color);
    background: rgba(234, 156, 0, 0.05);
  }

  .address-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  /* Checkout actions mobile */
  .checkout-actions {
    flex-direction: column;
    gap: 12px;
    margin-top: 25px;
    padding: 0 5px;
  }

  .checkout-actions .btn {
    width: 100%;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    min-height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
  }

  .checkout-actions .btn-primary {
    background: var(--primary-color);
    border: none;
    color: white;
  }

  .checkout-actions .btn-primary:hover {
    background: #d18a00;
    transform: translateY(-1px);
  }

  .checkout-actions .btn-secondary {
    background: white;
    border: 1px solid #ddd;
    color: var(--dark-color);
  }

  .checkout-actions .btn-secondary:hover {
    border-color: var(--dark-color);
    background: #f8f9fa;
  }

  /* Order summary mobile */
  .order-summary {
    background: white;
    border-radius: 12px;
    padding: 20px 15px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .order-summary h3 {
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
  }

  /* WooCommerce form fields mobile */
  .woocommerce-checkout .form-row {
    margin-bottom: 18px;
  }

  .woocommerce-checkout .form-row label {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
    display: block;
  }

  .woocommerce-checkout .input-text {
    padding: 14px 16px !important;
    font-size: 16px !important;
    border-radius: 10px !important;
    border: 1px solid #ddd !important;
    width: 100% !important;
  }

  .woocommerce-checkout .input-text:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1) !important;
    outline: none !important;
  }

  /* Payment methods mobile */
  .payment-methods {
    margin: 20px 0;
  }

  .payment-method {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    margin-bottom: 12px;
    padding: 16px;
    transition: all 0.3s ease;
  }

  .payment-method:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .payment-method.selected {
    border-color: var(--primary-color);
    background: rgba(234, 156, 0, 0.05);
  }

  .payment-method label {
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .payment-method input[type="radio"] {
    transform: scale(1.3);
  }
}

/* Extra Small Mobile Devices (480px and below) */
@media (max-width: 480px) {
  .checkout-container {
    padding: 10px 0;
  }

  .checkout-container .container {
    padding-left: 10px;
    padding-right: 10px;
  }

  .checkout-header h2 {
    font-size: 22px;
  }

  .checkout-header p {
    font-size: 13px;
  }

  .progress-step {
    max-width: 260px;
    padding: 10px 14px;
  }

  .step-icon {
    width: 36px;
    height: 36px;
  }

  .step-label {
    font-size: 13px;
  }

  .checkout-form {
    padding: 16px 12px;
    margin-bottom: 15px;
  }

  .checkout-form h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-control {
    padding: 12px 14px;
    font-size: 16px;
  }

  .checkout-actions .btn {
    padding: 14px 18px;
    font-size: 15px;
    min-height: 48px;
  }

  .order-summary {
    padding: 16px 12px;
  }

  .order-summary h3 {
    font-size: 16px;
  }

  .address-card {
    padding: 14px;
  }

  .payment-method {
    padding: 14px;
  }
}

/* Landscape Orientation for Mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .checkout-container {
    padding: 10px 0;
  }

  .checkout-header {
    margin-bottom: 15px;
  }

  .checkout-header h2 {
    font-size: 20px;
    margin-bottom: 5px;
  }

  .checkout-header p {
    font-size: 13px;
  }

  .checkout-progress {
    flex-direction: row;
    justify-content: center;
    margin-bottom: 20px;
    gap: 10px;
  }

  .progress-step {
    flex-direction: column;
    width: auto;
    max-width: 120px;
    padding: 8px 12px;
    text-align: center;
  }

  .step-icon {
    width: 32px;
    height: 32px;
    margin-right: 0;
    margin-bottom: 4px;
  }

  .step-label {
    font-size: 11px;
  }

  .checkout-form {
    padding: 15px 12px;
  }

  .checkout-form h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .form-group {
    margin-bottom: 14px;
  }

  .form-control {
    padding: 10px 12px;
    font-size: 14px;
  }

  .checkout-actions {
    flex-direction: row;
    gap: 10px;
    margin-top: 15px;
  }

  .checkout-actions .btn {
    padding: 12px 16px;
    font-size: 14px;
    min-height: 44px;
  }

  .order-summary {
    padding: 15px 12px;
    margin-top: 15px;
  }
}

/* High DPI / Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .step-icon {
    border-width: 0.5px;
  }

  .checkout-form {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .form-control {
    border-width: 0.5px;
  }
}

/* Dark Mode Support (if enabled) */
@media (prefers-color-scheme: dark) {
  @media (max-width: 768px) {
    .checkout-container {
      background-color: #1a1a1a;
    }

    .checkout-form,
    .order-summary,
    .progress-step {
      background: #2d2d2d;
      border-color: #404040;
    }

    .checkout-form h3,
    .order-summary h3,
    .step-label {
      color: #ffffff;
    }

    .form-control {
      background: #2d2d2d;
      border-color: #404040;
      color: #ffffff;
    }

    .form-control:focus {
      border-color: var(--primary-color);
      background: #2d2d2d;
    }

    .checkout-actions .btn-secondary {
      background: #2d2d2d;
      border-color: #404040;
      color: #ffffff;
    }
  }
}