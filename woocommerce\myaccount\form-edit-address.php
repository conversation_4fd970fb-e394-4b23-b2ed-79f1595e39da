<?php
/**
 * Edit address form - Modern Design
 *
 * This template displays the address edit form with a modern card-based design.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.3.0
 */

defined( 'ABSPATH' ) || exit;

$page_title = ( 'billing' === $load_address ) ? esc_html__( 'Billing address', 'woocommerce' ) : esc_html__( 'Shipping address', 'woocommerce' );

do_action( 'woocommerce_before_edit_account_address_form' ); ?>

<?php if ( ! $load_address ) : ?>
<?php wc_get_template( 'myaccount/my-address.php' ); ?>
<?php else : ?>

<div class="address-form-container">
  <div class="address-form-header">
    <i data-feather="edit-2" class="address-icon"></i>
    <h3 class="address-form-title">
      <?php echo apply_filters( 'woocommerce_my_account_edit_address_title', $page_title, $load_address ); ?></h3>
  </div>

  <form method="post" novalidate class="address-form">
    <div class="woocommerce-address-fields">
      <?php do_action( "woocommerce_before_edit_address_form_{$load_address}" ); ?>

      <div class="woocommerce-billing-fields__field-wrapper">
        <?php
					// Define required fields for address forms
					$required_fields = array(
						'first_name', 'last_name', 'address_1', 'city', 'postcode', 'country', 'state'
					);

					// Group fields for better layout
					$grouped_fields = array();
					$full_width_fields = array( 'address_1', 'address_2', 'company' );
					$row_fields = array();

					foreach ( $address as $key => $field ) {
						// Make necessary fields required
						if ( in_array( str_replace( $load_address . '_', '', $key ), $required_fields ) ) {
							$field['required'] = true;
						}

						if ( in_array( str_replace( $load_address . '_', '', $key ), $full_width_fields ) ) {
							$grouped_fields['full_width'][] = array( 'key' => $key, 'field' => $field );
						} else {
							$row_fields[] = array( 'key' => $key, 'field' => $field );
						}
					}

					// Group remaining fields in pairs
					$grouped_fields['rows'] = array_chunk( $row_fields, 2 );

					// Render full-width fields first
					if ( !empty( $grouped_fields['full_width'] ) ) {
						foreach ( $grouped_fields['full_width'] as $field_data ) {
							echo '<div class="address-form-row full-width">';
							echo '<div class="address-form-group">';

							// Customize field for our design
							$field_data['field']['class'][] = 'address-form-input';
							$field_data['field']['label_class'][] = 'address-form-label';

							woocommerce_form_field( $field_data['key'], $field_data['field'], wc_get_post_data_by_key( $field_data['key'], $field_data['field']['value'] ) );
							echo '</div>';
							echo '</div>';
						}
					}

					// Render paired fields
					if ( !empty( $grouped_fields['rows'] ) ) {
						foreach ( $grouped_fields['rows'] as $row ) {
							echo '<div class="address-form-row">';

							foreach ( $row as $field_data ) {
								echo '<div class="address-form-group">';

								// Customize field for our design
								$field_data['field']['class'][] = 'address-form-input';
								$field_data['field']['label_class'][] = 'address-form-label';

								woocommerce_form_field( $field_data['key'], $field_data['field'], wc_get_post_data_by_key( $field_data['key'], $field_data['field']['value'] ) );
								echo '</div>';
							}

							echo '</div>';
						}
					}
					?>
      </div>

      <?php do_action( "woocommerce_after_edit_address_form_{$load_address}" ); ?>

      <div class="address-form-actions">
        <a href="<?php echo esc_url( wc_get_endpoint_url( 'edit-address', '' ) ); ?>"
          class="address-btn address-btn-secondary">
          <i data-feather="arrow-left"></i>
          <?php esc_html_e( 'Back to Addresses', 'woocommerce' ); ?>
        </a>
        <button type="submit" class="address-btn address-btn-primary" name="save_address"
          value="<?php esc_attr_e( 'Save address', 'woocommerce' ); ?>">
          <i data-feather="save"></i>
          <?php esc_html_e( 'Save address', 'woocommerce' ); ?>
        </button>
        <?php wp_nonce_field( 'woocommerce-edit_address', 'woocommerce-edit-address-nonce' ); ?>
        <input type="hidden" name="action" value="edit_address" />
      </div>
    </div>
  </form>
</div>

<?php endif; ?>

<?php do_action( 'woocommerce_after_edit_account_address_form' ); ?>

<style>
/* Override WooCommerce default form styles */
.woocommerce-billing-fields__field-wrapper .form-row {
  margin: 0 !important;
  padding: 0 !important;
  float: none !important;
  width: 100% !important;
  clear: both !important;
}

.woocommerce-billing-fields__field-wrapper .form-row label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.woocommerce-billing-fields__field-wrapper .form-row input,
.woocommerce-billing-fields__field-wrapper .form-row select,
.woocommerce-billing-fields__field-wrapper .form-row textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: #fff;
}

.woocommerce-billing-fields__field-wrapper .form-row input:focus,
.woocommerce-billing-fields__field-wrapper .form-row select:focus,
.woocommerce-billing-fields__field-wrapper .form-row textarea:focus {
  outline: none;
  border-color: #ea9c00;
  box-shadow: 0 0 0 3px rgba(234, 156, 0, 0.1);
}

.woocommerce-billing-fields__field-wrapper .form-row .required {
  color: #ef4444;
}

/* Validation styles */
.woocommerce-billing-fields__field-wrapper .form-row input.is-invalid,
.woocommerce-billing-fields__field-wrapper .form-row select.is-invalid,
.woocommerce-billing-fields__field-wrapper .form-row textarea.is-invalid {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.woocommerce-billing-fields__field-wrapper .form-row input.is-valid,
.woocommerce-billing-fields__field-wrapper .form-row select.is-valid,
.woocommerce-billing-fields__field-wrapper .form-row textarea.is-valid {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Address form layout */
.address-form-container {
  max-width: 800px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.address-form-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background: linear-gradient(135deg, #ea9c00 0%, #f59e0b 100%);
  color: white;
}

.address-form-header .address-icon {
  width: 24px;
  height: 24px;
}

.address-form-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.address-form {
  padding: 24px;
}

.address-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.address-form-row.full-width {
  grid-template-columns: 1fr;
}

.address-form-group {
  display: flex;
  flex-direction: column;
}

.address-form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  margin-top: 24px;
}

.address-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.address-btn-primary {
  background: #ea9c00;
  color: white;
}

.address-btn-primary:hover {
  background: #d97706;
  color: white;
}

.address-btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.address-btn-secondary:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .address-form-row {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .address-form-actions {
    flex-direction: column !important;
  }

  .address-btn {
    width: 100% !important;
    justify-content: center !important;
  }

  .address-form-container {
    margin: 0 16px;
  }

  .address-form-header,
  .address-form {
    padding: 16px;
  }
}
</style>