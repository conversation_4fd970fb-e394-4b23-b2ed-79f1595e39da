<?php
/**
 * Checkout shipping information form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-shipping.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 * @global WC_Checkout $checkout
 */

defined( 'ABSPATH' ) || exit;
?>
<div class="woocommerce-shipping-fields">
	<?php if ( true === WC()->cart->needs_shipping_address() ) : ?>

		<h3 id="ship-to-different-address">
			<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
				<input id="ship-to-different-address-checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" <?php checked( apply_filters( 'woocommerce_ship_to_different_address_checked', 'shipping' === get_option( 'woocommerce_ship_to_destination' ) ? 1 : 0 ), 1 ); ?> type="checkbox" name="ship_to_different_address" value="1" /> <span><?php esc_html_e( 'Ship to a different address?', 'woocommerce' ); ?></span>
			</label>
		</h3>

		<div class="shipping_address">

			<?php do_action( 'woocommerce_before_checkout_shipping_form', $checkout ); ?>

			<?php if ( is_user_logged_in() ) : ?>
				<?php
				// Get user's saved shipping address
				$customer_id = get_current_user_id();
				$customer = new WC_Customer( $customer_id );
				$user_info = get_userdata( $customer_id );
				$customer_name = trim( $customer->get_first_name() . ' ' . $customer->get_last_name() );
				if ( empty( $customer_name ) ) {
					$customer_name = $user_info->display_name;
				}

				// Check if user has saved shipping address
				$has_shipping_address = ! empty( $customer->get_shipping_address_1() );
				?>

				<?php if ( $has_shipping_address ) : ?>
					<div class="saved-shipping-addresses-section">
						<h4><?php esc_html_e( 'Select Shipping Address', 'woocommerce' ); ?></h4>
						<div class="address-cards-grid">
							<?php
							// Display shipping address card
							$shipping_address = array(
								'name' => $customer_name,
								'address_1' => $customer->get_shipping_address_1(),
								'address_2' => $customer->get_shipping_address_2(),
								'city' => $customer->get_shipping_city(),
								'state' => $customer->get_shipping_state(),
								'postcode' => $customer->get_shipping_postcode(),
								'country' => $customer->get_shipping_country(),
							);
							?>
							<div class="address-card shipping-address-card" data-address-type="shipping" data-address-id="shipping_saved">
								<div class="address-card-header">
									<i data-feather="truck" class="address-icon"></i>
									<h4 class="address-name"><?php echo esc_html( $customer_name ); ?></h4>
								</div>
								<div class="address-card-type">
									<span class="address-type-badge"><?php esc_html_e( 'Shipping Address', 'woocommerce' ); ?></span>
								</div>
								<div class="address-card-content">
									<?php if ( ! empty( $shipping_address['address_1'] ) ) : ?>
										<div class="address-item">
											<i data-feather="map-pin" class="address-item-icon"></i>
											<span class="address-item-text">
												<?php echo esc_html( $shipping_address['address_1'] ); ?>
												<?php if ( ! empty( $shipping_address['address_2'] ) ) : ?>
													<br><?php echo esc_html( $shipping_address['address_2'] ); ?>
												<?php endif; ?>
											</span>
										</div>
									<?php endif; ?>

									<?php if ( ! empty( $shipping_address['city'] ) ) : ?>
										<div class="address-item">
											<i data-feather="map" class="address-item-icon"></i>
											<span class="address-item-text">
												<?php echo esc_html( $shipping_address['city'] ); ?>
												<?php if ( ! empty( $shipping_address['state'] ) ) : ?>
													, <?php echo esc_html( $shipping_address['state'] ); ?>
												<?php endif; ?>
												<?php if ( ! empty( $shipping_address['postcode'] ) ) : ?>
													<?php echo esc_html( $shipping_address['postcode'] ); ?>
												<?php endif; ?>
											</span>
										</div>
									<?php endif; ?>
								</div>
							</div>

							<!-- Add new shipping address option -->
							<div class="address-card add-new-shipping-address">
								<div class="address-empty">
									<div class="address-empty-icon">
										<i data-feather="plus"></i>
									</div>
									<p class="address-empty-text"><?php esc_html_e( 'Enter a New Shipping Address', 'woocommerce' ); ?></p>
								</div>
							</div>
						</div>

						<div class="or-divider shipping-or-divider">
							<span><?php esc_html_e( 'Or enter a new shipping address', 'woocommerce' ); ?></span>
						</div>
					</div>
				<?php endif; ?>
			<?php endif; ?>

			<div class="woocommerce-shipping-fields__field-wrapper <?php echo ( is_user_logged_in() && $has_shipping_address ) ? 'hidden-form' : ''; ?>">
				<?php
				$fields = $checkout->get_checkout_fields( 'shipping' );

				foreach ( $fields as $key => $field ) {
					woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
				}
				?>
			</div>

			<!-- Hidden input to store selected shipping address -->
			<input type="hidden" id="selected_shipping_address_id" name="selected_shipping_address_id" value="<?php echo ( is_user_logged_in() && $has_shipping_address ) ? 'shipping_saved' : ''; ?>" />

			<?php do_action( 'woocommerce_after_checkout_shipping_form', $checkout ); ?>

		</div>

	<?php endif; ?>
</div>
<div class="woocommerce-additional-fields">
	<?php do_action( 'woocommerce_before_order_notes', $checkout ); ?>

	<?php if ( apply_filters( 'woocommerce_enable_order_notes_field', 'yes' === get_option( 'woocommerce_enable_order_comments', 'yes' ) ) ) : ?>

		<?php if ( ! WC()->cart->needs_shipping() || wc_ship_to_billing_address_only() ) : ?>

			<h3><?php esc_html_e( 'Additional information', 'woocommerce' ); ?></h3>

		<?php endif; ?>

		<div class="woocommerce-additional-fields__field-wrapper">
			<?php foreach ( $checkout->get_checkout_fields( 'order' ) as $key => $field ) : ?>
				<?php woocommerce_form_field( $key, $field, $checkout->get_value( $key ) ); ?>
			<?php endforeach; ?>
		</div>

	<?php endif; ?>

	<?php do_action( 'woocommerce_after_order_notes', $checkout ); ?>
</div>
