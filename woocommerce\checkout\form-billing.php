<?php
/**
 * Checkout billing information form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-billing.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 * @global WC_Checkout $checkout
 */

defined( 'ABSPATH' ) || exit;
?>
<div class="woocommerce-billing-fields">
	<?php if ( wc_ship_to_billing_address_only() && WC()->cart->needs_shipping() ) : ?>

		<h3><?php esc_html_e( 'Billing &amp; Shipping', 'woocommerce' ); ?></h3>

	<?php else : ?>

		<h3><?php esc_html_e( 'Billing details', 'woocommerce' ); ?></h3>

	<?php endif; ?>

	<?php do_action( 'woocommerce_before_checkout_billing_form', $checkout ); ?>

	<?php if ( is_user_logged_in() ) : ?>
		<?php
		// Get user's saved addresses
		$customer_id = get_current_user_id();
		$customer = new WC_Customer( $customer_id );
		$user_info = get_userdata( $customer_id );
		$customer_name = trim( $customer->get_first_name() . ' ' . $customer->get_last_name() );
		if ( empty( $customer_name ) ) {
			$customer_name = $user_info->display_name;
		}

		// Check if user has saved billing address
		$has_billing_address = ! empty( $customer->get_billing_address_1() );
		$has_shipping_address = ! empty( $customer->get_shipping_address_1() );
		?>

		<?php if ( $has_billing_address ) : ?>
			<div class="saved-addresses-section">
				<h4><?php esc_html_e( 'Select Billing Address', 'woocommerce' ); ?></h4>
				<div class="address-cards-grid">
					<?php
					// Display billing address card
					$billing_address = array(
						'name' => $customer_name,
						'address_1' => $customer->get_billing_address_1(),
						'address_2' => $customer->get_billing_address_2(),
						'city' => $customer->get_billing_city(),
						'state' => $customer->get_billing_state(),
						'postcode' => $customer->get_billing_postcode(),
						'country' => $customer->get_billing_country(),
						'phone' => $customer->get_billing_phone(),
						'email' => $customer->get_billing_email(),
					);
					?>
					<div class="address-card" data-address-type="billing" data-address-id="billing_saved">
						<div class="address-card-header">
							<i data-feather="user" class="address-icon"></i>
							<h4 class="address-name"><?php echo esc_html( $customer_name ); ?></h4>
						</div>
						<div class="address-card-type">
							<span class="address-type-badge"><?php esc_html_e( 'Billing Address', 'woocommerce' ); ?></span>
						</div>
						<div class="address-card-content">
							<?php if ( ! empty( $billing_address['address_1'] ) ) : ?>
								<div class="address-item">
									<i data-feather="map-pin" class="address-item-icon"></i>
									<span class="address-item-text">
										<?php echo esc_html( $billing_address['address_1'] ); ?>
										<?php if ( ! empty( $billing_address['address_2'] ) ) : ?>
											<br><?php echo esc_html( $billing_address['address_2'] ); ?>
										<?php endif; ?>
									</span>
								</div>
							<?php endif; ?>

							<?php if ( ! empty( $billing_address['city'] ) ) : ?>
								<div class="address-item">
									<i data-feather="map" class="address-item-icon"></i>
									<span class="address-item-text">
										<?php echo esc_html( $billing_address['city'] ); ?>
										<?php if ( ! empty( $billing_address['state'] ) ) : ?>
											, <?php echo esc_html( $billing_address['state'] ); ?>
										<?php endif; ?>
										<?php if ( ! empty( $billing_address['postcode'] ) ) : ?>
											<?php echo esc_html( $billing_address['postcode'] ); ?>
										<?php endif; ?>
									</span>
								</div>
							<?php endif; ?>

							<?php if ( ! empty( $billing_address['phone'] ) ) : ?>
								<div class="address-item">
									<i data-feather="phone" class="address-item-icon"></i>
									<span class="address-item-text"><?php echo esc_html( $billing_address['phone'] ); ?></span>
								</div>
							<?php endif; ?>

							<?php if ( ! empty( $billing_address['email'] ) ) : ?>
								<div class="address-item">
									<i data-feather="at-sign" class="address-item-icon"></i>
									<span class="address-item-text"><?php echo esc_html( $billing_address['email'] ); ?></span>
								</div>
							<?php endif; ?>
						</div>
					</div>

					<!-- Add new address option -->
					<div class="address-card add-new-address">
						<div class="address-empty">
							<div class="address-empty-icon">
								<i data-feather="plus"></i>
							</div>
							<p class="address-empty-text"><?php esc_html_e( 'Enter a New Address', 'woocommerce' ); ?></p>
						</div>
					</div>
				</div>

				<div class="or-divider">
					<span><?php esc_html_e( 'Or enter a new address', 'woocommerce' ); ?></span>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<div class="woocommerce-billing-fields__field-wrapper <?php echo ( is_user_logged_in() && $has_billing_address ) ? 'hidden-form' : ''; ?>">
		<?php
		$fields = $checkout->get_checkout_fields( 'billing' );

		foreach ( $fields as $key => $field ) {
			woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
		}
		?>
	</div>

	<!-- Hidden input to store selected address -->
	<input type="hidden" id="selected_address_id" name="selected_address_id" value="<?php echo ( is_user_logged_in() && $has_billing_address ) ? 'billing_saved' : ''; ?>" />

	<?php do_action( 'woocommerce_after_checkout_billing_form', $checkout ); ?>
</div>

<?php if ( ! is_user_logged_in() && $checkout->is_registration_enabled() ) : ?>
	<div class="woocommerce-account-fields">
		<?php if ( ! $checkout->is_registration_required() ) : ?>

			<p class="form-row form-row-wide create-account">
				<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
					<input class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="createaccount" <?php checked( ( true === $checkout->get_value( 'createaccount' ) || ( true === apply_filters( 'woocommerce_create_account_default_checked', false ) ) ), true ); ?> type="checkbox" name="createaccount" value="1" /> <span><?php esc_html_e( 'Create an account?', 'woocommerce' ); ?></span>
				</label>
			</p>

		<?php endif; ?>

		<?php do_action( 'woocommerce_before_checkout_registration_form', $checkout ); ?>

		<?php if ( $checkout->get_checkout_fields( 'account' ) ) : ?>

			<div class="create-account">
				<?php foreach ( $checkout->get_checkout_fields( 'account' ) as $key => $field ) : ?>
					<?php woocommerce_form_field( $key, $field, $checkout->get_value( $key ) ); ?>
				<?php endforeach; ?>
				<div class="clear"></div>
			</div>

		<?php endif; ?>

		<?php do_action( 'woocommerce_after_checkout_registration_form', $checkout ); ?>
	</div>
<?php endif; ?>
