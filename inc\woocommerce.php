<?php
/**
 * Add WooCommerce support
 *
 * @package Understrap
 */

// Exit if accessed directly.
defined( 'ABSPATH' ) || exit;

add_action( 'after_setup_theme', 'tendeal_woocommerce_support' );

if ( ! function_exists( 'tendeal_woocommerce_support' ) ) {
	/**
	 * Declares WooCommerce theme support.
	 */
	function tendeal_woocommerce_support() {
		add_theme_support( 'woocommerce' );

		// Add Product Gallery support.
		// add_theme_support( 'wc-product-gallery-lightbox' );
		// add_theme_support( 'wc-product-gallery-zoom' );
		// add_theme_support( 'wc-product-gallery-slider' );

		// Add Bootstrap classes to form fields.
		// add_filter( 'woocommerce_form_field_args', 'understrap_wc_form_field_args', 10, 3 );
		// add_filter( 'woocommerce_form_field_radio', 'understrap_wc_form_field_radio', 10, 4 );
		// add_filter( 'woocommerce_quantity_input_classes', 'understrap_quantity_input_classes' );
		// add_filter( 'woocommerce_loop_add_to_cart_args', 'understrap_loop_add_to_cart_args' );

		// Wrap the add-to-cart link in `div.add-to-cart-container`.
		add_filter( 'woocommerce_loop_add_to_cart_link', 'tendeal_loop_add_to_cart_link' );

		// Add Bootstrap classes to account navigation.
		// add_filter( 'woocommerce_account_menu_item_classes', 'understrap_account_menu_item_classes' );

		// Customize address fields to make them required
		add_filter( 'woocommerce_billing_fields', 'tendeal_make_address_fields_required' );
		add_filter( 'woocommerce_shipping_fields', 'tendeal_make_address_fields_required' );

		// Also apply to default address fields
		add_filter( 'woocommerce_default_address_fields', 'tendeal_make_default_address_fields_required' );
	}
}

if ( ! function_exists( 'tendeal_loop_add_to_cart_link' ) ) {
	/**
	 * Wrap add to cart link in container.
	 *
	 * @param string $html Add to cart link HTML.
	 * @return string Add to cart link HTML.
	 */
	function tendeal_loop_add_to_cart_link( $html ) {
		return '<div class="row d-flex mb-3 ">  <div class="add-to-cart-container  col">' . $html . ' </div>';
	}
}

if ( ! function_exists( 'tendeal_make_address_fields_required' ) ) {
	/**
	 * Make necessary address fields required.
	 *
	 * @param array $fields Address fields array.
	 * @return array Modified address fields array.
	 */
	function tendeal_make_address_fields_required( $fields ) {
		// Define which fields should be required
		$required_fields = array(
			'first_name',
			'last_name',
			'address_1',
			'city',
			'postcode',
			'country',
			'state'
		);

		// Make specified fields required
		foreach ( $required_fields as $field_key ) {
			if ( isset( $fields[ $field_key ] ) ) {
				$fields[ $field_key ]['required'] = true;
			}
		}

		// Ensure proper validation classes are added
		foreach ( $fields as $key => $field ) {
			if ( isset( $field['required'] ) && $field['required'] ) {
				// Add required class if not already present
				if ( ! isset( $field['class'] ) ) {
					$fields[ $key ]['class'] = array();
				}
				if ( ! in_array( 'validate-required', $fields[ $key ]['class'] ) ) {
					$fields[ $key ]['class'][] = 'validate-required';
				}
			}
		}

		return $fields;
	}
}

if ( ! function_exists( 'tendeal_make_default_address_fields_required' ) ) {
	/**
	 * Make default address fields required.
	 *
	 * @param array $fields Default address fields array.
	 * @return array Modified address fields array.
	 */
	function tendeal_make_default_address_fields_required( $fields ) {
		// Define which default fields should be required
		$required_fields = array(
			'first_name',
			'last_name',
			'address_1',
			'city',
			'postcode',
			'country',
			'state'
		);

		// Make specified fields required
		foreach ( $required_fields as $field_key ) {
			if ( isset( $fields[ $field_key ] ) ) {
				$fields[ $field_key ]['required'] = true;

				// Add validation class
				if ( ! isset( $fields[ $field_key ]['class'] ) ) {
					$fields[ $field_key ]['class'] = array();
				}
				if ( ! in_array( 'validate-required', $fields[ $field_key ]['class'] ) ) {
					$fields[ $field_key ]['class'][] = 'validate-required';
				}
			}
		}

		return $fields;
	}
}

if ( ! function_exists( 'tendeal_validate_address_fields' ) ) {
	/**
	 * Validate address fields on form submission.
	 *
	 * @param array $data Posted data.
	 * @param WP_Error $errors Error object.
	 * @param WC_Customer $customer Customer object.
	 */
	function tendeal_validate_address_fields( $data, $errors, $customer ) {
		// Only run this validation on account pages, not checkout
		if ( is_checkout() || ( isset( $_POST['woocommerce_checkout_place_order'] ) ) ) {
			return;
		}

		// Define required fields for validation
		$required_fields = array(
			'billing_first_name' => __( 'Billing First Name', 'woocommerce' ),
			'billing_last_name'  => __( 'Billing Last Name', 'woocommerce' ),
			'billing_address_1'  => __( 'Billing Address', 'woocommerce' ),
			'billing_city'       => __( 'Billing City', 'woocommerce' ),
			'billing_postcode'   => __( 'Billing Postcode', 'woocommerce' ),
			'billing_country'    => __( 'Billing Country', 'woocommerce' ),
			'billing_state'      => __( 'Billing State', 'woocommerce' ),
			'shipping_first_name' => __( 'Shipping First Name', 'woocommerce' ),
			'shipping_last_name'  => __( 'Shipping Last Name', 'woocommerce' ),
			'shipping_address_1'  => __( 'Shipping Address', 'woocommerce' ),
			'shipping_city'       => __( 'Shipping City', 'woocommerce' ),
			'shipping_postcode'   => __( 'Shipping Postcode', 'woocommerce' ),
			'shipping_country'    => __( 'Shipping Country', 'woocommerce' ),
			'shipping_state'      => __( 'Shipping State', 'woocommerce' ),
		);

		// Check if we're editing an address
		$address_type = '';
		if ( isset( $_POST['action'] ) && $_POST['action'] === 'edit_address' ) {
			// Determine which address type we're editing
			$current_section = WC()->query->get_current_endpoint();
			if ( in_array( $current_section, array( 'billing', 'shipping' ) ) ) {
				$address_type = $current_section;
			}
		}

		// Validate fields based on address type being edited
		foreach ( $required_fields as $field_key => $field_label ) {
			// Skip validation if we're editing a specific address type and this field doesn't match
			if ( ! empty( $address_type ) ) {
				if ( strpos( $field_key, $address_type . '_' ) !== 0 ) {
					continue;
				}
			}

			// Check if field is empty
			if ( empty( $data[ $field_key ] ) ) {
				$errors->add( 'validation', sprintf( __( '%s is a required field.', 'woocommerce' ), '<strong>' . $field_label . '</strong>' ) );
			}
		}
	}
}

// Hook the validation function
add_action( 'woocommerce_save_account_details_errors', 'tendeal_validate_address_fields', 10, 3 );

if ( ! function_exists( 'tendeal_validate_edit_address' ) ) {
	/**
	 * Validate address fields when editing address specifically.
	 */
	function tendeal_validate_edit_address() {
		// Only run on account pages, not checkout
		if ( is_checkout() || ( isset( $_POST['woocommerce_checkout_place_order'] ) ) ) {
			return;
		}

		// Check if we're processing an address edit
		if ( isset( $_POST['save_address'] ) && isset( $_POST['action'] ) && $_POST['action'] === 'edit_address' ) {
			// Get the address type being edited
			$load_address = '';
			if ( isset( $_GET['address'] ) ) {
				$load_address = sanitize_text_field( $_GET['address'] );
			}

			if ( empty( $load_address ) || ! in_array( $load_address, array( 'billing', 'shipping' ) ) ) {
				return;
			}

			// Define required fields based on address type
			$required_fields = array(
				'first_name' => __( 'First Name', 'woocommerce' ),
				'last_name'  => __( 'Last Name', 'woocommerce' ),
				'address_1'  => __( 'Address', 'woocommerce' ),
				'city'       => __( 'City', 'woocommerce' ),
				'postcode'   => __( 'Postcode', 'woocommerce' ),
				'country'    => __( 'Country', 'woocommerce' ),
				'state'      => __( 'State', 'woocommerce' ),
			);

			$errors = array();

			// Validate each required field
			foreach ( $required_fields as $field_key => $field_label ) {
				$full_field_key = $load_address . '_' . $field_key;
				if ( empty( $_POST[ $full_field_key ] ) ) {
					$errors[] = sprintf( __( '%s is a required field.', 'woocommerce' ), '<strong>' . $field_label . '</strong>' );
				}
			}

			// If there are errors, display them and prevent saving
			if ( ! empty( $errors ) ) {
				foreach ( $errors as $error ) {
					wc_add_notice( $error, 'error' );
				}
				// Prevent the default save action
				return false;
			}
		}
	}
}

// Hook the address edit validation
add_action( 'template_redirect', 'tendeal_validate_edit_address', 5 );

if ( ! function_exists( 'tendeal_process_checkout_address_selection' ) ) {
	/**
	 * Process selected address in checkout.
	 */
	function tendeal_process_checkout_address_selection() {
		// Check if a saved billing address was selected
		if ( isset( $_POST['selected_address_id'] ) && ! empty( $_POST['selected_address_id'] ) ) {
			$selected_address = sanitize_text_field( $_POST['selected_address_id'] );

			// If billing_saved is selected, populate billing fields with saved data
			if ( $selected_address === 'billing_saved' && is_user_logged_in() ) {
				$customer = new WC_Customer( get_current_user_id() );

				// Only override if the customer has saved data
				if ( ! empty( $customer->get_billing_address_1() ) ) {
					$_POST['billing_first_name'] = $customer->get_billing_first_name();
					$_POST['billing_last_name'] = $customer->get_billing_last_name();
					$_POST['billing_company'] = $customer->get_billing_company();
					$_POST['billing_address_1'] = $customer->get_billing_address_1();
					$_POST['billing_address_2'] = $customer->get_billing_address_2();
					$_POST['billing_city'] = $customer->get_billing_city();
					$_POST['billing_state'] = $customer->get_billing_state();
					$_POST['billing_postcode'] = $customer->get_billing_postcode();
					$_POST['billing_country'] = $customer->get_billing_country();
					$_POST['billing_phone'] = $customer->get_billing_phone();
					$_POST['billing_email'] = $customer->get_billing_email();
				}
			}
		}

		// Check if a saved shipping address was selected
		if ( isset( $_POST['selected_shipping_address_id'] ) && ! empty( $_POST['selected_shipping_address_id'] ) ) {
			$selected_shipping_address = sanitize_text_field( $_POST['selected_shipping_address_id'] );

			// If shipping_saved is selected, populate shipping fields with saved data
			if ( $selected_shipping_address === 'shipping_saved' && is_user_logged_in() ) {
				$customer = new WC_Customer( get_current_user_id() );

				// Only override if the customer has saved shipping data
				if ( ! empty( $customer->get_shipping_address_1() ) ) {
					$_POST['shipping_first_name'] = $customer->get_shipping_first_name();
					$_POST['shipping_last_name'] = $customer->get_shipping_last_name();
					$_POST['shipping_company'] = $customer->get_shipping_company();
					$_POST['shipping_address_1'] = $customer->get_shipping_address_1();
					$_POST['shipping_address_2'] = $customer->get_shipping_address_2();
					$_POST['shipping_city'] = $customer->get_shipping_city();
					$_POST['shipping_state'] = $customer->get_shipping_state();
					$_POST['shipping_postcode'] = $customer->get_shipping_postcode();
					$_POST['shipping_country'] = $customer->get_shipping_country();
				}
			}
		}
	}
}

// Hook the checkout address processing - use early priority to run before validation
add_action( 'woocommerce_checkout_process', 'tendeal_process_checkout_address_selection', 1 );

if ( ! function_exists( 'tendeal_modify_checkout_posted_data' ) ) {
	/**
	 * Modify checkout posted data to use saved addresses.
	 *
	 * @param array $data Posted checkout data.
	 * @return array Modified checkout data.
	 */
	function tendeal_modify_checkout_posted_data( $data ) {
		// Check if a saved billing address was selected
		if ( isset( $data['selected_address_id'] ) && $data['selected_address_id'] === 'billing_saved' && is_user_logged_in() ) {
			$customer = new WC_Customer( get_current_user_id() );

			// Only override if the customer has saved data
			if ( ! empty( $customer->get_billing_address_1() ) ) {
				$data['billing_first_name'] = $customer->get_billing_first_name();
				$data['billing_last_name'] = $customer->get_billing_last_name();
				$data['billing_company'] = $customer->get_billing_company();
				$data['billing_address_1'] = $customer->get_billing_address_1();
				$data['billing_address_2'] = $customer->get_billing_address_2();
				$data['billing_city'] = $customer->get_billing_city();
				$data['billing_state'] = $customer->get_billing_state();
				$data['billing_postcode'] = $customer->get_billing_postcode();
				$data['billing_country'] = $customer->get_billing_country();
				$data['billing_phone'] = $customer->get_billing_phone();
				$data['billing_email'] = $customer->get_billing_email();

				// Handle any custom fields that might be missing
				// Check for common custom field patterns
				$custom_field_patterns = array(
					'billing_delivery_location',
					'billing_location',
					'delivery_location',
					'billing_area',
					'billing_zone'
				);

				foreach ( $custom_field_patterns as $field_name ) {
					// If the field exists in checkout fields but not in posted data, try to get it from customer meta
					$checkout_fields = WC()->checkout()->get_checkout_fields( 'billing' );
					if ( isset( $checkout_fields[ $field_name ] ) && empty( $data[ $field_name ] ) ) {
						$customer_meta_value = get_user_meta( get_current_user_id(), $field_name, true );
						if ( ! empty( $customer_meta_value ) ) {
							$data[ $field_name ] = $customer_meta_value;
						} else {
							// If no saved value, use the billing city as fallback for location fields
							if ( strpos( $field_name, 'location' ) !== false || strpos( $field_name, 'area' ) !== false ) {
								$data[ $field_name ] = $customer->get_billing_city();
							}
						}
					}
				}
			}
		}

		// Check if a saved shipping address was selected
		if ( isset( $data['selected_shipping_address_id'] ) && $data['selected_shipping_address_id'] === 'shipping_saved' && is_user_logged_in() ) {
			$customer = new WC_Customer( get_current_user_id() );

			// Only override if the customer has saved shipping data
			if ( ! empty( $customer->get_shipping_address_1() ) ) {
				$data['shipping_first_name'] = $customer->get_shipping_first_name();
				$data['shipping_last_name'] = $customer->get_shipping_last_name();
				$data['shipping_company'] = $customer->get_shipping_company();
				$data['shipping_address_1'] = $customer->get_shipping_address_1();
				$data['shipping_address_2'] = $customer->get_shipping_address_2();
				$data['shipping_city'] = $customer->get_shipping_city();
				$data['shipping_state'] = $customer->get_shipping_state();
				$data['shipping_postcode'] = $customer->get_shipping_postcode();
				$data['shipping_country'] = $customer->get_shipping_country();
			}
		}

		return $data;
	}
}

// Hook the checkout data modification
add_filter( 'woocommerce_checkout_posted_data', 'tendeal_modify_checkout_posted_data', 10, 1 );

if ( ! function_exists( 'tendeal_debug_checkout_errors' ) ) {
	/**
	 * Debug checkout errors for troubleshooting.
	 */
	function tendeal_debug_checkout_errors() {
		// Log checkout data for debugging (always enabled for now)
		if ( isset( $_POST['woocommerce_checkout_place_order'] ) ) {
			error_log( 'Checkout Debug - Posted Data: ' . print_r( $_POST, true ) );

			// Check for selected addresses
			if ( isset( $_POST['selected_address_id'] ) ) {
				error_log( 'Checkout Debug - Selected Billing Address: ' . $_POST['selected_address_id'] );
			}
			if ( isset( $_POST['selected_shipping_address_id'] ) ) {
				error_log( 'Checkout Debug - Selected Shipping Address: ' . $_POST['selected_shipping_address_id'] );
			}

			// Log all checkout fields to identify the "Delivery Location" field
			if ( WC()->checkout() ) {
				$checkout_fields = WC()->checkout()->get_checkout_fields();
				error_log( 'Checkout Debug - All Checkout Fields: ' . print_r( $checkout_fields, true ) );
			}
		}
	}
}

// Hook the debug function
add_action( 'woocommerce_checkout_process', 'tendeal_debug_checkout_errors', 999 );

if ( ! function_exists( 'tendeal_validate_checkout_address_selection' ) ) {
	/**
	 * Validate that required address fields are present when using saved addresses.
	 */
	function tendeal_validate_checkout_address_selection() {
		// Skip validation if not using saved addresses
		if ( empty( $_POST['selected_address_id'] ) && empty( $_POST['selected_shipping_address_id'] ) ) {
			return;
		}

		// Validate billing address if selected
		if ( isset( $_POST['selected_address_id'] ) && $_POST['selected_address_id'] === 'billing_saved' ) {
			if ( ! is_user_logged_in() ) {
				wc_add_notice( __( 'You must be logged in to use saved addresses.', 'woocommerce' ), 'error' );
				return;
			}

			$customer = new WC_Customer( get_current_user_id() );
			if ( empty( $customer->get_billing_address_1() ) ) {
				wc_add_notice( __( 'Selected billing address is incomplete. Please enter a new address.', 'woocommerce' ), 'error' );
			}
		}

		// Validate shipping address if selected
		if ( isset( $_POST['selected_shipping_address_id'] ) && $_POST['selected_shipping_address_id'] === 'shipping_saved' ) {
			if ( ! is_user_logged_in() ) {
				wc_add_notice( __( 'You must be logged in to use saved addresses.', 'woocommerce' ), 'error' );
				return;
			}

			$customer = new WC_Customer( get_current_user_id() );
			if ( empty( $customer->get_shipping_address_1() ) ) {
				wc_add_notice( __( 'Selected shipping address is incomplete. Please enter a new address.', 'woocommerce' ), 'error' );
			}
		}
	}
}

// Hook the validation function
add_action( 'woocommerce_checkout_process', 'tendeal_validate_checkout_address_selection', 20 );

if ( ! function_exists( 'tendeal_checkout_error_handler' ) ) {
	/**
	 * Handle checkout errors and provide better error messages.
	 */
	function tendeal_checkout_error_handler( $errors ) {
		// If there are validation errors, provide more specific messages
		if ( ! empty( $errors ) ) {
			// Check if it's related to address selection
			if ( isset( $_POST['selected_address_id'] ) || isset( $_POST['selected_shipping_address_id'] ) ) {
				// Add a notice about address selection issues
				wc_add_notice( __( 'There was an issue with your selected address. Please verify your address information and try again.', 'woocommerce' ), 'error' );
			}
		}
	}
}

// Hook the error handler
add_action( 'woocommerce_checkout_validation', 'tendeal_checkout_error_handler', 999 );

if ( ! function_exists( 'tendeal_remove_conflicting_validation' ) ) {
	/**
	 * Temporarily disable our custom validation during checkout to prevent conflicts.
	 */
	function tendeal_remove_conflicting_validation() {
		if ( is_checkout() ) {
			// Remove our custom validation hooks during checkout
			remove_action( 'woocommerce_save_account_details_errors', 'tendeal_validate_address_fields', 10 );
			remove_action( 'template_redirect', 'tendeal_validate_edit_address', 5 );
		}
	}
}

// Hook to remove conflicting validation
add_action( 'init', 'tendeal_remove_conflicting_validation', 1 );

if ( ! function_exists( 'tendeal_handle_missing_checkout_fields' ) ) {
	/**
	 * Handle missing required fields when using saved addresses.
	 */
	function tendeal_handle_missing_checkout_fields() {
		// Only run during checkout
		if ( ! is_checkout() || ! isset( $_POST['woocommerce_checkout_place_order'] ) ) {
			return;
		}

		// Check if using saved addresses
		$using_saved_billing = isset( $_POST['selected_address_id'] ) && $_POST['selected_address_id'] === 'billing_saved';
		$using_saved_shipping = isset( $_POST['selected_shipping_address_id'] ) && $_POST['selected_shipping_address_id'] === 'shipping_saved';

		if ( ! $using_saved_billing && ! $using_saved_shipping ) {
			return;
		}

		// Get all checkout fields
		$checkout_fields = WC()->checkout()->get_checkout_fields();
		$customer = new WC_Customer( get_current_user_id() );

		// Handle billing fields
		if ( $using_saved_billing && isset( $checkout_fields['billing'] ) ) {
			foreach ( $checkout_fields['billing'] as $field_key => $field_config ) {
				// If field is required but empty in POST data
				if ( isset( $field_config['required'] ) && $field_config['required'] && empty( $_POST[ $field_key ] ) ) {
					// Try to get value from customer object first
					$method_name = 'get_' . $field_key;
					if ( method_exists( $customer, $method_name ) ) {
						$value = $customer->$method_name();
						if ( ! empty( $value ) ) {
							$_POST[ $field_key ] = $value;
							continue;
						}
					}

					// Try to get from user meta
					$meta_value = get_user_meta( get_current_user_id(), $field_key, true );
					if ( ! empty( $meta_value ) ) {
						$_POST[ $field_key ] = $meta_value;
						continue;
					}

					// For location/area fields, use city as fallback
					if ( strpos( $field_key, 'location' ) !== false || strpos( $field_key, 'area' ) !== false || strpos( $field_key, 'zone' ) !== false ) {
						$city = $customer->get_billing_city();
						if ( ! empty( $city ) ) {
							$_POST[ $field_key ] = $city;
						}
					}
				}
			}
		}

		// Handle shipping fields similarly
		if ( $using_saved_shipping && isset( $checkout_fields['shipping'] ) ) {
			foreach ( $checkout_fields['shipping'] as $field_key => $field_config ) {
				if ( isset( $field_config['required'] ) && $field_config['required'] && empty( $_POST[ $field_key ] ) ) {
					$method_name = 'get_' . $field_key;
					if ( method_exists( $customer, $method_name ) ) {
						$value = $customer->$method_name();
						if ( ! empty( $value ) ) {
							$_POST[ $field_key ] = $value;
							continue;
						}
					}

					$meta_value = get_user_meta( get_current_user_id(), $field_key, true );
					if ( ! empty( $meta_value ) ) {
						$_POST[ $field_key ] = $meta_value;
						continue;
					}

					if ( strpos( $field_key, 'location' ) !== false || strpos( $field_key, 'area' ) !== false || strpos( $field_key, 'zone' ) !== false ) {
						$city = $customer->get_shipping_city();
						if ( ! empty( $city ) ) {
							$_POST[ $field_key ] = $city;
						}
					}
				}
			}
		}
	}
}

// Hook the missing fields handler - run very early
add_action( 'woocommerce_checkout_process', 'tendeal_handle_missing_checkout_fields', 1 );

if ( ! function_exists( 'tendeal_bypass_delivery_location_validation' ) ) {
	/**
	 * Temporarily bypass delivery location validation for saved addresses.
	 */
	function tendeal_bypass_delivery_location_validation( $fields ) {
		// If using saved addresses, make delivery location fields optional temporarily
		if ( ( isset( $_POST['selected_address_id'] ) && $_POST['selected_address_id'] === 'billing_saved' ) ||
		     ( isset( $_POST['selected_shipping_address_id'] ) && $_POST['selected_shipping_address_id'] === 'shipping_saved' ) ) {

			// Common delivery location field names
			$location_fields = array(
				'billing_delivery_location',
				'billing_location',
				'delivery_location',
				'billing_area',
				'billing_zone',
				'shipping_delivery_location',
				'shipping_location',
				'shipping_area',
				'shipping_zone'
			);

			foreach ( $location_fields as $field_name ) {
				if ( isset( $fields[ $field_name ] ) ) {
					// Make the field optional and provide a default value
					$fields[ $field_name ]['required'] = false;
					if ( empty( $_POST[ $field_name ] ) ) {
						// Use city as default for location fields
						if ( strpos( $field_name, 'billing' ) !== false ) {
							$customer = new WC_Customer( get_current_user_id() );
							$_POST[ $field_name ] = $customer->get_billing_city();
						} elseif ( strpos( $field_name, 'shipping' ) !== false ) {
							$customer = new WC_Customer( get_current_user_id() );
							$_POST[ $field_name ] = $customer->get_shipping_city();
						}
					}
				}
			}
		}

		return $fields;
	}
}

// Hook the delivery location bypass
add_filter( 'woocommerce_billing_fields', 'tendeal_bypass_delivery_location_validation', 999 );
add_filter( 'woocommerce_shipping_fields', 'tendeal_bypass_delivery_location_validation', 999 );