/**
 * Checkout Page JavaScript
 */
jQuery(document).ready(function($) {
  // Initialize Feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }

  // Variables
  const $checkoutSteps = $('.checkout-step');
  const $progressSteps = $('.progress-step');
  const $nextButtons = $('.btn-next');
  const $prevButtons = $('.btn-prev');

  const $paymentMethods = $('.payment-method');
  const $shipToDifferentCheckbox = $('#ship-to-different-address-checkbox');
  const $addressCards = $('.address-card:not(.add-new-address):not(.add-new-shipping-address)');
  const $shippingAddressCards = $('.shipping-address-card');
  const $shippingAddressFields = $('.shipping_address');

  // Initialize checkout
  initCheckout();

  // Handle address card selection
  $addressCards.on('click', function() {
    if (!$(this).hasClass('add-new-address')) {
      $addressCards.removeClass('selected');
      $(this).addClass('selected');

      // Update hidden input with selected address ID
      const addressId = $(this).data('address-id') || $(this).data('address-type');
      $('#selected_address_id').val(addressId);

      // Hide the manual address form when a saved address is selected
      $('.woocommerce-billing-fields__field-wrapper').addClass('hidden-form');
      $('.saved-addresses-section').addClass('active-selection');

      // If billing address is selected, populate the form fields (for backend processing)
      if (addressId === 'billing_saved') {
        populateBillingFields();
      }
    }
  });

  // Handle "Enter a New Address" card click
  $('.add-new-address').on('click', function() {
    $addressCards.removeClass('selected');
    $('#selected_address_id').val('');
    $('.woocommerce-billing-fields__field-wrapper').removeClass('hidden-form');
    $('.saved-addresses-section').removeClass('active-selection');
  });

  // Handle "Enter a New Shipping Address" card click
  $('.add-new-shipping-address').on('click', function() {
    $shippingAddressCards.removeClass('selected');
    $('#selected_shipping_address_id').val('');
    $('.woocommerce-shipping-fields__field-wrapper').removeClass('hidden-form');
    $('.saved-shipping-addresses-section').removeClass('active-selection');
  });

  // Handle shipping address card selection
  $shippingAddressCards.on('click', function() {
    if (!$(this).hasClass('add-new-shipping-address')) {
      $shippingAddressCards.removeClass('selected');
      $(this).addClass('selected');

      // Update hidden input with selected shipping address ID
      const addressId = $(this).data('address-id') || $(this).data('address-type');
      $('#selected_shipping_address_id').val(addressId);

      // Hide the manual shipping address form when a saved address is selected
      $('.woocommerce-shipping-fields__field-wrapper').addClass('hidden-form');
      $('.saved-shipping-addresses-section').addClass('active-selection');

      // If shipping address is selected, populate the form fields (for backend processing)
      if (addressId === 'shipping_saved') {
        populateShippingFields();
      }
    }
  });

  // Show manual address form when "Or enter a new address" divider is clicked
  $('.or-divider').on('click', function() {
    $addressCards.removeClass('selected');
    $('#selected_address_id').val('');
    $('.woocommerce-billing-fields__field-wrapper').removeClass('hidden-form');
    $('.saved-addresses-section').removeClass('active-selection');
  });

  // Show manual shipping address form when "Or enter a new shipping address" divider is clicked
  $('.shipping-or-divider').on('click', function() {
    $shippingAddressCards.removeClass('selected');
    $('#selected_shipping_address_id').val('');
    $('.woocommerce-shipping-fields__field-wrapper').removeClass('hidden-form');
    $('.saved-shipping-addresses-section').removeClass('active-selection');
  });

  // Set default address as selected if available
  const $defaultAddress = $('.address-card .default-badge').closest('.address-card');
  if ($defaultAddress.length) {
    $defaultAddress.trigger('click');
  } else {
    // If no default, select the first saved address if available
    const $firstSavedAddress = $('.address-card[data-address-id="billing_saved"]');
    if ($firstSavedAddress.length) {
      $firstSavedAddress.trigger('click');
    }
  }

  /**
   * Initialize checkout functionality
   */
  function initCheckout() {
    // Show first step, hide others
    showStep(0);

    // Handle next button clicks
    $nextButtons.on('click', function(e) {
      e.preventDefault();
      const currentStep = $(this).closest('.checkout-step').data('step');

      // Validate current step
      if (validateStep(currentStep)) {
        showStep(currentStep + 1);
      }
    });

    // Handle previous button clicks
    $prevButtons.on('click', function(e) {
      e.preventDefault();
      const currentStep = $(this).closest('.checkout-step').data('step');
      showStep(currentStep - 1);
    });



    // Handle payment method selection
    $paymentMethods.on('click', function() {
      $paymentMethods.removeClass('selected');
      $(this).addClass('selected');

      // Update hidden input with selected payment method
      $('#selected_payment_method').val($(this).data('payment-method'));
    });

    // Handle ship to different address checkbox
    $shipToDifferentCheckbox.on('change', function() {
      if ($(this).is(':checked')) {
        $shippingAddressFields.slideDown();
      } else {
        $shippingAddressFields.slideUp();
      }
    });



    // Initialize form validation
    initFormValidation();
  }

  /**
   * Show checkout step by index
   * @param {number} stepIndex - The index of the step to show
   */
  function showStep(stepIndex) {
    // Hide all steps
    $checkoutSteps.hide();

    // Show the requested step
    $checkoutSteps.eq(stepIndex).show();

    // Update progress indicator
    updateProgress(stepIndex);

    // Scroll to top
    $('html, body').animate({
      scrollTop: $('.checkout-container').offset().top - 50
    }, 300);
  }

  /**
   * Update progress indicator
   * @param {number} currentStep - The current step index
   */
  function updateProgress(currentStep) {
    $progressSteps.removeClass('active completed');

    // Mark steps as active or completed
    $progressSteps.each(function(index) {
      if (index === currentStep) {
        $(this).addClass('active');
      } else if (index < currentStep) {
        $(this).addClass('completed');
      }
    });
  }

  /**
   * Validate current step
   * @param {number} stepIndex - The index of the step to validate
   * @returns {boolean} - Whether the step is valid
   */
  function validateStep(stepIndex) {
    let isValid = true;

    switch(stepIndex) {
      case 0: // Shipping details
        // Check if a saved address is selected
        const hasSelectedAddress = $addressCards.filter('.selected').length > 0;

        // If no saved address is selected, validate the manual address form
        if (!hasSelectedAddress) {
          // Validate required billing fields
          const $requiredFields = $('.woocommerce-billing-fields__field-wrapper').find('[required]');
          $requiredFields.each(function() {
            if ($(this).val().trim() === '') {
              $(this).addClass('is-invalid');
              isValid = false;
            } else {
              $(this).removeClass('is-invalid');
            }
          });

          // Validate shipping fields if shipping to different address
          if ($shipToDifferentCheckbox.is(':checked')) {
            const $requiredShippingFields = $('.shipping_address').find('[required]');
            $requiredShippingFields.each(function() {
              if ($(this).val().trim() === '') {
                $(this).addClass('is-invalid');
                isValid = false;
              } else {
                $(this).removeClass('is-invalid');
              }
            });
          }

          if (!isValid) {
            showError('Please fill in all required address fields.');
          }
        }
        break;

      case 1: // Payment details
        // Check if a payment method is selected
        if ($paymentMethods.filter('.selected').length === 0) {
          isValid = false;
          showError('Please select a payment method.');
        }
        break;
    }

    return isValid;
  }

  /**
   * Validate address form
   * @returns {boolean} - Whether the form is valid
   */
  function validateAddressForm() {
    let isValid = true;
    const requiredFields = $('#address-form').find('[required]');

    requiredFields.each(function() {
      if ($(this).val().trim() === '') {
        isValid = false;
        $(this).addClass('is-invalid');
      } else {
        $(this).removeClass('is-invalid');
      }
    });

    return isValid;
  }

  /**
   * Initialize form validation
   */
  function initFormValidation() {
    // Add validation for required fields
    $('input[required], select[required], textarea[required]').on('blur', function() {
      if ($(this).val().trim() === '') {
        $(this).addClass('is-invalid');
      } else {
        $(this).removeClass('is-invalid');
      }
    });

    // Remove validation error on input
    $('input, select, textarea').on('input', function() {
      $(this).removeClass('is-invalid');
    });
  }

  /**
   * Populate billing fields with saved address data
   */
  function populateBillingFields() {
    // This function can be used to populate form fields if needed
    // For now, we rely on server-side processing of the selected address
    console.log('Billing address selected');
  }

  /**
   * Populate shipping fields with saved address data
   */
  function populateShippingFields() {
    // This function can be used to populate form fields if needed
    // For now, we rely on server-side processing of the selected address
    console.log('Shipping address selected');
  }

  /**
   * Show error message
   * @param {string} message - The error message
   */
  function showError(message) {
    // Create a more user-friendly notification
    if ($('.woocommerce-notices-wrapper').length) {
      $('.woocommerce-notices-wrapper').html(
        '<div class="woocommerce-error" role="alert">' + message + '</div>'
      );
      $('html, body').animate({
        scrollTop: $('.woocommerce-notices-wrapper').offset().top - 100
      }, 500);
    } else {
      alert(message);
    }
  }

  /**
   * Initialize address card styling and behavior
   */
  function initAddressCards() {
    // Initialize Feather icons for address cards
    if (typeof feather !== 'undefined') {
      feather.replace();
    }

    // Add hover effects
    $('.address-card').hover(
      function() {
        if (!$(this).hasClass('selected')) {
          $(this).addClass('hover');
        }
      },
      function() {
        $(this).removeClass('hover');
      }
    );
  }

  // Initialize address cards on page load
  initAddressCards();
});
