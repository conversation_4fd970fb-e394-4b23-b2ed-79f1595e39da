/**
 * Checkout Page JavaScript
 */
jQuery(document).ready(function($) {
  // Initialize Feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }

  // Variables
  const $checkoutSteps = $('.checkout-step');
  const $progressSteps = $('.progress-step');
  const $nextButtons = $('.btn-next');
  const $prevButtons = $('.btn-prev');

  const $paymentMethods = $('.payment-method');
  const $shipToDifferentCheckbox = $('#ship-to-different-address-checkbox');
  const $addressCards = $('.address-card:not(.add-new-address)');
  const $shippingAddressFields = $('.shipping_address');

  // Initialize checkout
  initCheckout();

  // Handle address card selection
  $addressCards.on('click', function() {
    if (!$(this).hasClass('add-new-address')) {
      $addressCards.removeClass('selected');
      $(this).addClass('selected');

      // Update hidden input with selected address ID
      $('#selected_address_id').val($(this).data('address-id'));

      // Hide the manual address form when a saved address is selected
      $('.woocommerce-billing-fields__field-wrapper').addClass('hidden-form');
      $('.saved-addresses-section').addClass('active-selection');
    }
  });

  // Show manual address form when "Enter a New Address" is clicked
  $('.or-divider').on('click', function() {
    $addressCards.removeClass('selected');
    $('#selected_address_id').val('');
    $('.woocommerce-billing-fields__field-wrapper').removeClass('hidden-form');
    $('.saved-addresses-section').removeClass('active-selection');
  });

  // Set default address as selected if available
  const $defaultAddress = $('.address-card .default-badge').closest('.address-card');
  if ($defaultAddress.length) {
    $defaultAddress.trigger('click');
  }
      'Austria': 'AT',
      'Sweden': 'SE',
      'Norway': 'NO',
      'Denmark': 'DK',
      'Finland': 'FI',
      'Poland': 'PL',
      'Czech Republic': 'CZ',
      'Hungary': 'HU',
      'Romania': 'RO',
      'Bulgaria': 'BG',
      'Greece': 'GR',
      'Portugal': 'PT',
      'Ireland': 'IE',
      'Luxembourg': 'LU',
      'Malta': 'MT',
      'Cyprus': 'CY',
      'Estonia': 'EE',
      'Latvia': 'LV',
      'Lithuania': 'LT',
      'Slovenia': 'SI',
      'Slovakia': 'SK',
      'Croatia': 'HR',
      'Saudi Arabia': 'SA',
      'United Arab Emirates': 'AE',
      'Qatar': 'QA',
      'Kuwait': 'KW',
      'Bahrain': 'BH',
      'Oman': 'OM',
      'Jordan': 'JO',
      'Lebanon': 'LB',
      'Egypt': 'EG',
      'Morocco': 'MA',
      'Tunisia': 'TN',
      'Algeria': 'DZ',
      'Libya': 'LY',
      'Sudan': 'SD',
      'South Africa': 'ZA',
      'Nigeria': 'NG',
      'Kenya': 'KE',
      'Ghana': 'GH',
      'Ethiopia': 'ET',
      'Uganda': 'UG',
      'Tanzania': 'TZ',
      'Zimbabwe': 'ZW',
      'Botswana': 'BW',
      'Namibia': 'NA',
      'Zambia': 'ZM',
      'Malawi': 'MW',
      'Mozambique': 'MZ',
      'Madagascar': 'MG',
      'Mauritius': 'MU',
      'Seychelles': 'SC',
      'India': 'IN',
      'China': 'CN',
      'Japan': 'JP',
      'South Korea': 'KR',
      'Thailand': 'TH',
      'Vietnam': 'VN',
      'Malaysia': 'MY',
      'Singapore': 'SG',
      'Indonesia': 'ID',
      'Philippines': 'PH',
      'Taiwan': 'TW',
      'Hong Kong': 'HK',
      'Macau': 'MO',
      'Mongolia': 'MN',
      'Myanmar': 'MM',
      'Cambodia': 'KH',
      'Laos': 'LA',
      'Brunei': 'BN',
      'Bangladesh': 'BD',
      'Sri Lanka': 'LK',
      'Nepal': 'NP',
      'Bhutan': 'BT',
      'Maldives': 'MV',
      'Pakistan': 'PK',
      'Afghanistan': 'AF',
      'Iran': 'IR',
      'Iraq': 'IQ',
      'Turkey': 'TR',
      'Israel': 'IL',
      'Palestine': 'PS',
      'Syria': 'SY',
      'Yemen': 'YE',
      'Brazil': 'BR',
      'Argentina': 'AR',
      'Chile': 'CL',
      'Colombia': 'CO',
      'Peru': 'PE',
      'Venezuela': 'VE',
      'Ecuador': 'EC',
      'Bolivia': 'BO',
      'Paraguay': 'PY',
      'Uruguay': 'UY',
      'Guyana': 'GY',
      'Suriname': 'SR',
      'French Guiana': 'GF',
      'Mexico': 'MX',
      'Guatemala': 'GT',
      'Belize': 'BZ',
      'El Salvador': 'SV',
      'Honduras': 'HN',
      'Nicaragua': 'NI',
      'Costa Rica': 'CR',
      'Panama': 'PA',
      'Cuba': 'CU',
      'Jamaica': 'JM',
      'Haiti': 'HT',
      'Dominican Republic': 'DO',
      'Puerto Rico': 'PR',
      'Trinidad and Tobago': 'TT',
      'Barbados': 'BB',
      'Bahamas': 'BS',
      'Russia': 'RU',
      'Ukraine': 'UA',
      'Belarus': 'BY',
      'Moldova': 'MD',
      'Georgia': 'GE',
      'Armenia': 'AM',
      'Azerbaijan': 'AZ',
      'Kazakhstan': 'KZ',
      'Uzbekistan': 'UZ',
      'Turkmenistan': 'TM',
      'Tajikistan': 'TJ',
      'Kyrgyzstan': 'KG'
    };

    return countryMappings[countryName] || null;
  }

  /**
   * Initialize checkout functionality
   */
  function initCheckout() {
    // Show first step, hide others
    showStep(0);

    // Handle next button clicks
    $nextButtons.on('click', function(e) {
      e.preventDefault();
      const currentStep = $(this).closest('.checkout-step').data('step');

      // Validate current step
      if (validateStep(currentStep)) {
        showStep(currentStep + 1);
      }
    });

    // Handle previous button clicks
    $prevButtons.on('click', function(e) {
      e.preventDefault();
      const currentStep = $(this).closest('.checkout-step').data('step');
      showStep(currentStep - 1);
    });



    // Handle payment method selection
    $paymentMethods.on('click', function() {
      $paymentMethods.removeClass('selected');
      $(this).addClass('selected');

      // Update hidden input with selected payment method
      $('#selected_payment_method').val($(this).data('payment-method'));
    });

    // Handle ship to different address checkbox
    $shipToDifferentCheckbox.on('change', function() {
      if ($(this).is(':checked')) {
        $shippingAddressFields.slideDown();
      } else {
        $shippingAddressFields.slideUp();
      }
    });



    // Initialize form validation
    initFormValidation();
  }

  /**
   * Show checkout step by index
   * @param {number} stepIndex - The index of the step to show
   */
  function showStep(stepIndex) {
    // Hide all steps
    $checkoutSteps.hide();

    // Show the requested step
    $checkoutSteps.eq(stepIndex).show();

    // Update progress indicator
    updateProgress(stepIndex);

    // Scroll to top
    $('html, body').animate({
      scrollTop: $('.checkout-container').offset().top - 50
    }, 300);
  }

  /**
   * Update progress indicator
   * @param {number} currentStep - The current step index
   */
  function updateProgress(currentStep) {
    $progressSteps.removeClass('active completed');

    // Mark steps as active or completed
    $progressSteps.each(function(index) {
      if (index === currentStep) {
        $(this).addClass('active');
      } else if (index < currentStep) {
        $(this).addClass('completed');
      }
    });
  }

  /**
   * Validate current step
   * @param {number} stepIndex - The index of the step to validate
   * @returns {boolean} - Whether the step is valid
   */
  function validateStep(stepIndex) {
    let isValid = true;

    switch(stepIndex) {
      case 0: // Shipping details
        // Check if a saved address is selected
        const hasSelectedAddress = $addressCards.filter('.selected').length > 0;

        // If no saved address is selected, validate the manual address form
        if (!hasSelectedAddress) {
          // Validate required billing fields
          const $requiredFields = $('.woocommerce-billing-fields__field-wrapper').find('[required]');
          $requiredFields.each(function() {
            if ($(this).val().trim() === '') {
              $(this).addClass('is-invalid');
              isValid = false;
            } else {
              $(this).removeClass('is-invalid');
            }
          });

          // Validate shipping fields if shipping to different address
          if ($shipToDifferentCheckbox.is(':checked')) {
            const $requiredShippingFields = $('.shipping_address').find('[required]');
            $requiredShippingFields.each(function() {
              if ($(this).val().trim() === '') {
                $(this).addClass('is-invalid');
                isValid = false;
              } else {
                $(this).removeClass('is-invalid');
              }
            });
          }

          if (!isValid) {
            showError('Please fill in all required address fields.');
          }
        }
        break;

      case 1: // Payment details
        // Check if a payment method is selected
        if ($paymentMethods.filter('.selected').length === 0) {
          isValid = false;
          showError('Please select a payment method.');
        }
        break;
    }

    return isValid;
  }

  /**
   * Validate address form
   * @returns {boolean} - Whether the form is valid
   */
  function validateAddressForm() {
    let isValid = true;
    const requiredFields = $('#address-form').find('[required]');

    requiredFields.each(function() {
      if ($(this).val().trim() === '') {
        isValid = false;
        $(this).addClass('is-invalid');
      } else {
        $(this).removeClass('is-invalid');
      }
    });

    return isValid;
  }

  /**
   * Initialize form validation
   */
  function initFormValidation() {
    // Add validation for required fields
    $('input[required], select[required], textarea[required]').on('blur', function() {
      if ($(this).val().trim() === '') {
        $(this).addClass('is-invalid');
      } else {
        $(this).removeClass('is-invalid');
      }
    });

    // Remove validation error on input
    $('input, select, textarea').on('input', function() {
      $(this).removeClass('is-invalid');
    });
  }

  /**
   * Show error message
   * @param {string} message - The error message
   */
  function showError(message) {
    // Implement your error notification here
    alert(message);
  }
});
